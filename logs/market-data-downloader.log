2025-06-03 00:20:01 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 00:20:01 [Test worker] INFO  c.i.service.BollingerBandService - Found 1 symbols with OHLCV data out of 1 total instruments in database
2025-06-03 00:20:01 [Test worker] WARN  c.i.service.BollingerBandService - No records updated for symbol: AAPL
2025-06-03 00:20:01 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 0 symbols processed, 0 records updated, 0 failed, 0 insufficient data, 0 skipped in 21ms
2025-06-03 00:20:01 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=FULL_RECALCULATION, forceRecalculate=false (deprecated)}
2025-06-03 00:20:01 [Test worker] INFO  c.i.service.BollingerBandService - Found 1 symbols with OHLCV data out of 1 total instruments in database
2025-06-03 00:20:01 [Test worker] WARN  c.i.service.BollingerBandService - No records updated for symbol: AAPL
2025-06-03 00:20:01 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 0 symbols processed, 0 records updated, 0 failed, 0 insufficient data, 0 skipped in 1ms
2025-06-03 00:20:01 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=SKIP_EXISTING, forceRecalculate=false (deprecated)}
2025-06-03 00:20:01 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 2 total instruments in database
2025-06-03 00:20:01 [Test worker] WARN  c.i.service.BollingerBandService - No records updated for symbol: MSFT
2025-06-03 00:20:01 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 0 symbols processed, 0 records updated, 0 failed, 0 insufficient data, 1 skipped in 1ms
2025-06-03 00:20:01 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=FULL_RECALCULATION, forceRecalculate=true (deprecated)}
2025-06-03 00:20:01 [Test worker] INFO  c.i.service.BollingerBandService - Found 1 symbols with OHLCV data out of 1 total instruments in database
2025-06-03 00:20:01 [Test worker] WARN  c.i.service.BollingerBandService - No records updated for symbol: AAPL
2025-06-03 00:20:01 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 0 symbols processed, 0 records updated, 0 failed, 0 insufficient data, 0 skipped in 1ms
2025-06-03 00:20:01 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=SKIP_EXISTING, forceRecalculate=false (deprecated)}
2025-06-03 00:20:01 [Test worker] INFO  c.i.service.BollingerBandService - Found 1 symbols with OHLCV data out of 1 total instruments in database
2025-06-03 00:20:01 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 0 symbols processed, 0 records updated, 0 failed, 0 insufficient data, 1 skipped in 0ms
2025-06-03 00:20:01 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=true, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 00:20:01 [Test worker] INFO  c.i.service.BollingerBandService - Found 1 symbols with OHLCV data out of 1 total instruments in database
2025-06-03 00:20:01 [Test worker] WARN  c.i.service.BollingerBandService - No records updated for symbol: AAPL
2025-06-03 00:20:01 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 0 symbols processed, 0 records updated, 0 failed, 0 insufficient data, 0 skipped in 0ms
2025-06-03 00:20:34 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 00:20:34 [Test worker] INFO  c.i.service.BollingerBandService - Found 1 symbols with OHLCV data out of 1 total instruments in database
2025-06-03 00:20:34 [Test worker] WARN  c.i.service.BollingerBandService - No records updated for symbol: AAPL
2025-06-03 00:20:34 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 0 symbols processed, 0 records updated, 0 failed, 0 insufficient data, 0 skipped in 13ms
2025-06-03 00:22:51 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 00:22:51 [Test worker] INFO  c.i.service.BollingerBandService - Found 1 symbols with OHLCV data out of 1 total instruments in database
2025-06-03 00:22:51 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 10 records updated
2025-06-03 00:22:51 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 1 symbols processed, 10 records updated, 0 failed, 0 insufficient data, 0 skipped in 12ms
2025-06-03 00:22:51 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=FULL_RECALCULATION, forceRecalculate=false (deprecated)}
2025-06-03 00:22:51 [Test worker] INFO  c.i.service.BollingerBandService - Found 1 symbols with OHLCV data out of 1 total instruments in database
2025-06-03 00:22:51 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 25 records updated
2025-06-03 00:22:51 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 1 symbols processed, 25 records updated, 0 failed, 0 insufficient data, 0 skipped in 0ms
2025-06-03 00:22:51 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=SKIP_EXISTING, forceRecalculate=false (deprecated)}
2025-06-03 00:22:51 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 2 total instruments in database
2025-06-03 00:22:51 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 15 records updated
2025-06-03 00:22:51 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 1 symbols processed, 15 records updated, 0 failed, 0 insufficient data, 1 skipped in 1ms
2025-06-03 00:22:51 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=FULL_RECALCULATION, forceRecalculate=true (deprecated)}
2025-06-03 00:22:51 [Test worker] INFO  c.i.service.BollingerBandService - Found 1 symbols with OHLCV data out of 1 total instruments in database
2025-06-03 00:22:51 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 25 records updated
2025-06-03 00:22:51 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 1 symbols processed, 25 records updated, 0 failed, 0 insufficient data, 0 skipped in 0ms
2025-06-03 00:22:51 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=SKIP_EXISTING, forceRecalculate=false (deprecated)}
2025-06-03 00:22:51 [Test worker] INFO  c.i.service.BollingerBandService - Found 1 symbols with OHLCV data out of 1 total instruments in database
2025-06-03 00:22:51 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 0 symbols processed, 0 records updated, 0 failed, 0 insufficient data, 1 skipped in 0ms
2025-06-03 00:22:51 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=true, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 00:22:51 [Test worker] INFO  c.i.service.BollingerBandService - Found 1 symbols with OHLCV data out of 1 total instruments in database
2025-06-03 00:22:51 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 5 records updated
2025-06-03 00:22:51 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 1 symbols processed, 5 records updated, 0 failed, 0 insufficient data, 0 skipped in 0ms
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=SKIP_EXISTING, forceRecalculate=false (deprecated)}
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 3 symbols with OHLCV data out of 5 total instruments in database
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Optimization: Processing only symbols with OHLCV data, skipping 2 instruments without price data
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 100 records updated
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 150 records updated
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for GOOGL: 200 records updated
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 3 symbols processed, 450 records updated, 0 failed, 0 insufficient data, 0 skipped in 11ms
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=50, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 3 symbols with OHLCV data out of 3 total instruments in database
2025-06-03 00:23:07 [Test worker] WARN  c.i.service.BollingerBandService - No records updated for symbol: AAPL
2025-06-03 00:23:07 [Test worker] WARN  c.i.service.BollingerBandService - No records updated for symbol: MSFT
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 0 symbols processed, 0 records updated, 0 failed, 1 insufficient data, 0 skipped in 3ms
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=SKIP_EXISTING, forceRecalculate=false (deprecated)}
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 2 total instruments in database
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 150 records updated
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 1 symbols processed, 150 records updated, 0 failed, 0 insufficient data, 1 skipped in 0ms
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 3 symbols with OHLCV data out of 3 total instruments in database
2025-06-03 00:23:07 [Test worker] WARN  c.i.service.BollingerBandService - No records updated for symbol: AAPL
2025-06-03 00:23:07 [Test worker] WARN  c.i.service.BollingerBandService - No records updated for symbol: FAIL
2025-06-03 00:23:07 [Test worker] WARN  c.i.service.BollingerBandService - No records updated for symbol: MSFT
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 0 symbols processed, 0 records updated, 0 failed, 0 insufficient data, 0 skipped in 1ms
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=2, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 4 symbols with OHLCV data out of 4 total instruments in database
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Limited processing to 2 symbols
2025-06-03 00:23:07 [Test worker] WARN  c.i.service.BollingerBandService - No records updated for symbol: AAPL
2025-06-03 00:23:07 [Test worker] WARN  c.i.service.BollingerBandService - No records updated for symbol: MSFT
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 0 symbols processed, 0 records updated, 0 failed, 0 insufficient data, 0 skipped in 1ms
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=true, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 2 total instruments in database
2025-06-03 00:23:07 [Test worker] WARN  c.i.service.BollingerBandService - No records updated for symbol: AAPL
2025-06-03 00:23:07 [Test worker] WARN  c.i.service.BollingerBandService - No records updated for symbol: MSFT
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 0 symbols processed, 0 records updated, 0 failed, 0 insufficient data, 0 skipped in 0ms
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 00:23:07 [Test worker] ERROR c.i.service.BollingerBandService - Fatal error during Bollinger Band calculation
java.lang.RuntimeException: Database connection failed
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.service.BollingerBandServiceSpec$__spock_feature_0_6_closure2.doCall(BollingerBandServiceSpec.groovy:194)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.database.DatabaseManager$SpockMock$1068870387.getSymbolsWithOhlcvData(Unknown Source)
	at com.investment.service.BollingerBandService.calculateBollingerBands(BollingerBandService.java:50)
	at com.investment.service.BollingerBandService$calculateBollingerBands.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.service.BollingerBandServiceSpec.$spock_feature_0_6(BollingerBandServiceSpec.groovy:197)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 5 total instruments in database
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Optimization: Processing only symbols with OHLCV data, skipping 3 instruments without price data
2025-06-03 00:23:07 [Test worker] WARN  c.i.service.BollingerBandService - No records updated for symbol: AAPL
2025-06-03 00:23:07 [Test worker] WARN  c.i.service.BollingerBandService - No records updated for symbol: MSFT
2025-06-03 00:23:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 0 symbols processed, 0 records updated, 0 failed, 0 insufficient data, 0 skipped in 0ms
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=SKIP_EXISTING, forceRecalculate=false (deprecated)}
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Found 3 symbols with OHLCV data out of 5 total instruments in database
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Optimization: Processing only symbols with OHLCV data, skipping 2 instruments without price data
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 100 records updated
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 150 records updated
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for GOOGL: 200 records updated
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 3 symbols processed, 450 records updated, 0 failed, 0 insufficient data, 0 skipped in 11ms
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=50, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Found 3 symbols with OHLCV data out of 3 total instruments in database
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 100 records updated
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 75 records updated
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 175 records updated, 0 failed, 1 insufficient data, 0 skipped in 1ms
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=SKIP_EXISTING, forceRecalculate=false (deprecated)}
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 2 total instruments in database
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 150 records updated
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 1 symbols processed, 150 records updated, 0 failed, 0 insufficient data, 1 skipped in 1ms
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Found 3 symbols with OHLCV data out of 3 total instruments in database
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 100 records updated
2025-06-03 01:27:15 [Test worker] ERROR c.i.service.BollingerBandService - Error processing symbol FAIL
java.sql.SQLException: Database error
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.service.BollingerBandServiceSpec$__spock_feature_0_3_closure1.doCall(BollingerBandServiceSpec.groovy:118)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.database.DatabaseManager$SpockMock$1193326306.calculateAndUpdateBollingerBandsIncremental(Unknown Source)
	at com.investment.service.BollingerBandService.calculateBollingerBandsForSymbol(BollingerBandService.java:198)
	at com.investment.service.BollingerBandService.calculateBollingerBands(BollingerBandService.java:93)
	at com.investment.service.BollingerBandService$calculateBollingerBands.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.service.BollingerBandServiceSpec.$spock_feature_0_3(BollingerBandServiceSpec.groovy:122)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 150 records updated
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 250 records updated, 1 failed, 0 insufficient data, 0 skipped in 11ms
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=2, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Found 4 symbols with OHLCV data out of 4 total instruments in database
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Limited processing to 2 symbols
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 100 records updated
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 150 records updated
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 250 records updated, 0 failed, 0 insufficient data, 0 skipped in 0ms
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=true, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 2 total instruments in database
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 100 records updated
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 150 records updated
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 250 records updated, 0 failed, 0 insufficient data, 0 skipped in 0ms
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:27:15 [Test worker] ERROR c.i.service.BollingerBandService - Fatal error during Bollinger Band calculation
java.lang.RuntimeException: Database connection failed
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.service.BollingerBandServiceSpec$__spock_feature_0_6_closure2.doCall(BollingerBandServiceSpec.groovy:185)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.database.DatabaseManager$SpockMock$1193326306.getSymbolsWithOhlcvData(Unknown Source)
	at com.investment.service.BollingerBandService.calculateBollingerBands(BollingerBandService.java:50)
	at com.investment.service.BollingerBandService$calculateBollingerBands.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.service.BollingerBandServiceSpec.$spock_feature_0_6(BollingerBandServiceSpec.groovy:188)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 5 total instruments in database
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Optimization: Processing only symbols with OHLCV data, skipping 3 instruments without price data
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 100 records updated
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 150 records updated
2025-06-03 01:27:15 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 250 records updated, 0 failed, 0 insufficient data, 0 skipped in 1ms
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Found 1 symbols with OHLCV data out of 1 total instruments in database
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 10 records updated
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 1 symbols processed, 10 records updated, 0 failed, 0 insufficient data, 0 skipped in 12ms
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=FULL_RECALCULATION, forceRecalculate=false (deprecated)}
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Found 1 symbols with OHLCV data out of 1 total instruments in database
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 25 records updated
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 1 symbols processed, 25 records updated, 0 failed, 0 insufficient data, 0 skipped in 0ms
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=SKIP_EXISTING, forceRecalculate=false (deprecated)}
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 2 total instruments in database
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 15 records updated
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 1 symbols processed, 15 records updated, 0 failed, 0 insufficient data, 1 skipped in 1ms
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=FULL_RECALCULATION, forceRecalculate=true (deprecated)}
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Found 1 symbols with OHLCV data out of 1 total instruments in database
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 25 records updated
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 1 symbols processed, 25 records updated, 0 failed, 0 insufficient data, 0 skipped in 0ms
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=SKIP_EXISTING, forceRecalculate=false (deprecated)}
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Found 1 symbols with OHLCV data out of 1 total instruments in database
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 0 symbols processed, 0 records updated, 0 failed, 0 insufficient data, 1 skipped in 0ms
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=true, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Found 1 symbols with OHLCV data out of 1 total instruments in database
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 5 records updated
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 1 symbols processed, 5 records updated, 0 failed, 0 insufficient data, 0 skipped in 1ms
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=SKIP_EXISTING, forceRecalculate=false (deprecated)}
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Found 3 symbols with OHLCV data out of 5 total instruments in database
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Optimization: Processing only symbols with OHLCV data, skipping 2 instruments without price data
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 100 records updated
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 150 records updated
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for GOOGL: 200 records updated
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 3 symbols processed, 450 records updated, 0 failed, 0 insufficient data, 0 skipped in 1ms
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=50, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Found 3 symbols with OHLCV data out of 3 total instruments in database
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 100 records updated
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 75 records updated
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 175 records updated, 0 failed, 1 insufficient data, 0 skipped in 0ms
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=SKIP_EXISTING, forceRecalculate=false (deprecated)}
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 2 total instruments in database
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 150 records updated
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 1 symbols processed, 150 records updated, 0 failed, 0 insufficient data, 1 skipped in 0ms
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Found 3 symbols with OHLCV data out of 3 total instruments in database
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 100 records updated
2025-06-03 01:28:00 [Test worker] ERROR c.i.service.BollingerBandService - Error processing symbol FAIL
java.sql.SQLException: Database error
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.service.BollingerBandServiceSpec$__spock_feature_0_3_closure1.doCall(BollingerBandServiceSpec.groovy:118)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.database.DatabaseManager$SpockMock$1915759902.calculateAndUpdateBollingerBandsIncremental(Unknown Source)
	at com.investment.service.BollingerBandService.calculateBollingerBandsForSymbol(BollingerBandService.java:198)
	at com.investment.service.BollingerBandService.calculateBollingerBands(BollingerBandService.java:93)
	at com.investment.service.BollingerBandService$calculateBollingerBands.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.service.BollingerBandServiceSpec.$spock_feature_0_3(BollingerBandServiceSpec.groovy:122)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 150 records updated
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 250 records updated, 1 failed, 0 insufficient data, 0 skipped in 8ms
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=2, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Found 4 symbols with OHLCV data out of 4 total instruments in database
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Limited processing to 2 symbols
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 100 records updated
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 150 records updated
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 250 records updated, 0 failed, 0 insufficient data, 0 skipped in 1ms
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=true, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 2 total instruments in database
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 100 records updated
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 150 records updated
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 250 records updated, 0 failed, 0 insufficient data, 0 skipped in 0ms
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:28:00 [Test worker] ERROR c.i.service.BollingerBandService - Fatal error during Bollinger Band calculation
java.lang.RuntimeException: Database connection failed
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.service.BollingerBandServiceSpec$__spock_feature_0_6_closure2.doCall(BollingerBandServiceSpec.groovy:185)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.database.DatabaseManager$SpockMock$1915759902.getSymbolsWithOhlcvData(Unknown Source)
	at com.investment.service.BollingerBandService.calculateBollingerBands(BollingerBandService.java:50)
	at com.investment.service.BollingerBandService$calculateBollingerBands.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.service.BollingerBandServiceSpec.$spock_feature_0_6(BollingerBandServiceSpec.groovy:188)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 5 total instruments in database
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Optimization: Processing only symbols with OHLCV data, skipping 3 instruments without price data
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 100 records updated
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 150 records updated
2025-06-03 01:28:00 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 250 records updated, 0 failed, 0 insufficient data, 0 skipped in 0ms
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Connected to the database
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 3
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Schema already up to date
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 3 - adding Bollinger Band columns to OHLCV table
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Bollinger Band columns already exist in OHLCV table
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Saved 25 OHLCV data points for AAPL
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Saved 22 OHLCV data points for MSFT
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Saved 5 OHLCV data points for NEWCO
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Database connection closed
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Connected to the database
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 3
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Schema already up to date
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 3 - adding Bollinger Band columns to OHLCV table
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Bollinger Band columns already exist in OHLCV table
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Saved 25 OHLCV data points for AAPL
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Saved 22 OHLCV data points for MSFT
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Saved 5 OHLCV data points for NEWCO
2025-06-03 01:28:01 [Test worker] INFO  c.i.database.DatabaseManager - Database connection closed
2025-06-03 01:34:31 [Test worker] INFO  c.i.database.DatabaseManager - Connected to the database
2025-06-03 01:34:31 [Test worker] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-06-03 01:34:31 [Test worker] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-06-03 01:34:31 [Test worker] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 3
2025-06-03 01:34:31 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-06-03 01:34:31 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-06-03 01:34:31 [Test worker] INFO  c.i.database.DatabaseManager - Schema already up to date
2025-06-03 01:34:31 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 3 - adding Bollinger Band columns to OHLCV table
2025-06-03 01:34:31 [Test worker] INFO  c.i.database.DatabaseManager - Bollinger Band columns already exist in OHLCV table
2025-06-03 01:34:31 [Test worker] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-06-03 01:34:31 [Test worker] INFO  c.i.database.DatabaseManager - Database connection closed
2025-06-03 01:35:31 [Test worker] INFO  c.i.database.DatabaseManager - Connected to the database
2025-06-03 01:35:31 [Test worker] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-06-03 01:35:31 [Test worker] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-06-03 01:35:31 [Test worker] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 3
2025-06-03 01:35:31 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-06-03 01:35:31 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-06-03 01:35:31 [Test worker] INFO  c.i.database.DatabaseManager - Schema already up to date
2025-06-03 01:35:31 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 3 - adding Bollinger Band columns to OHLCV table
2025-06-03 01:35:31 [Test worker] INFO  c.i.database.DatabaseManager - Bollinger Band columns already exist in OHLCV table
2025-06-03 01:35:31 [Test worker] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-06-03 01:35:31 [Test worker] INFO  c.i.database.DatabaseManager - Saved 50 OHLCV data points for AAPL
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=FULL_RECALCULATION, forceRecalculate=false (deprecated)}
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 2 total instruments in database
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 25 records updated
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 25 records updated
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 50 records updated, 0 failed, 0 insufficient data, 0 skipped in 26ms
2025-06-03 01:35:31 [Test worker] INFO  c.i.database.DatabaseManager - Saved 10 OHLCV data points for AAPL
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 2 total instruments in database
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 5 records updated
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 5 records updated
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 10 records updated, 0 failed, 0 insufficient data, 0 skipped in 15ms
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=FULL_RECALCULATION, forceRecalculate=false (deprecated)}
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 2 total instruments in database
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 30 records updated
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 30 records updated
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 60 records updated, 0 failed, 0 insufficient data, 0 skipped in 20ms
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=SKIP_EXISTING, forceRecalculate=false (deprecated)}
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 2 total instruments in database
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 0 symbols processed, 0 records updated, 0 failed, 0 insufficient data, 2 skipped in 4ms
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=FULL_RECALCULATION, forceRecalculate=true (deprecated)}
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 2 total instruments in database
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 30 records updated
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 30 records updated
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 60 records updated, 0 failed, 0 insufficient data, 0 skipped in 18ms
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=true, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 2 total instruments in database
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 19 records updated
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 19 records updated
2025-06-03 01:35:31 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 38 records updated, 0 failed, 0 insufficient data, 0 skipped in 4ms
2025-06-03 01:35:31 [Test worker] INFO  c.i.database.DatabaseManager - Database connection closed
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - Starting symbol validation (dry-run) - forceRefresh: false
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - Symbol validation (dry-run) completed: DRY RUN: Found 2 invalid symbols out of 10 total. Would delete 2 instruments and 15 OHLCV records.
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - Starting symbol validation (dry-run) - forceRefresh: true
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - Symbol validation (dry-run) completed: DRY RUN: Found 0 invalid symbols out of 5 total. Would delete 0 instruments and 0 OHLCV records.
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - Starting symbol validation (dry-run) - forceRefresh: false
2025-06-03 01:35:43 [Test worker] ERROR c.i.a.c.InstrumentController - Error during symbol validation (dry-run)
java.lang.RuntimeException: Service error
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.api.controller.InstrumentControllerSpec$__spock_feature_0_2_closure1.doCall(InstrumentControllerSpec.groovy:66)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.service.SymbolValidationService$SpockMock$1606729024.validateSymbols(Unknown Source)
	at com.investment.api.controller.InstrumentController.validateSymbolsDryRun(InstrumentController.java:78)
	at com.investment.api.controller.InstrumentController$validateSymbolsDryRun.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.InstrumentControllerSpec.$spock_feature_0_2(InstrumentControllerSpec.groovy:69)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - Starting symbol validation - dryRun: false, forceRefresh: false
2025-06-03 01:35:43 [Test worker] WARN  c.i.a.c.InstrumentController - PERFORMING ACTUAL CLEANUP - This will permanently delete invalid symbols and their data
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - Symbol cleanup completed: CLEANUP COMPLETED: Deleted 2 invalid symbols out of 10 total. Removed 2 instruments and 15 OHLCV records.
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - Starting symbol validation - dryRun: true, forceRefresh: false
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - Symbol validation (dry-run) completed: DRY RUN: Found 2 invalid symbols out of 10 total. Would delete 2 instruments and 15 OHLCV records.
2025-06-03 01:35:43 [Test worker] ERROR c.i.a.c.InstrumentController - Error retrieving SEC cache status
java.lang.RuntimeException: Cache error
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.api.controller.InstrumentControllerSpec$__spock_feature_0_7_closure2.doCall(InstrumentControllerSpec.groovy:141)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.service.SymbolValidationService$SpockMock$1606729024.getCacheStatus(Unknown Source)
	at com.investment.api.controller.InstrumentController.getSecCacheStatus(InstrumentController.java:175)
	at com.investment.api.controller.InstrumentController$getSecCacheStatus$1.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:130)
	at com.investment.api.controller.InstrumentControllerSpec.$spock_feature_0_7(InstrumentControllerSpec.groovy:144)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - Starting SEC synchronization (dry-run) - forceRefresh: false, maxInstruments: 1000
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - SEC synchronization (dry-run) completed: DRY RUN: Found 50 missing symbols out of 5000 SEC symbols. Would add 50 new instruments to database.
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - Starting SEC synchronization (dry-run) - forceRefresh: true, maxInstruments: 500
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - SEC synchronization (dry-run) completed: DRY RUN: Found 25 missing symbols out of 5000 SEC symbols. Would add 25 new instruments to database.
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - Starting SEC synchronization (dry-run) - forceRefresh: false, maxInstruments: 1000
2025-06-03 01:35:43 [Test worker] ERROR c.i.a.c.InstrumentController - Error during SEC synchronization (dry-run)
java.lang.RuntimeException: SEC API error
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.api.controller.InstrumentControllerSpec$__spock_feature_0_10_closure3.doCall(InstrumentControllerSpec.groovy:185)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.service.SecSynchronizationService$SpockMock$556441800.synchronizeInstruments(Unknown Source)
	at com.investment.api.controller.InstrumentController.syncSecDataDryRun(InstrumentController.java:218)
	at com.investment.api.controller.InstrumentController$syncSecDataDryRun$2.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:148)
	at com.investment.api.controller.InstrumentControllerSpec.$spock_feature_0_10(InstrumentControllerSpec.groovy:188)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - Starting SEC synchronization - dryRun: false, forceRefresh: false, maxInstruments: 100
2025-06-03 01:35:43 [Test worker] WARN  c.i.a.c.InstrumentController - PERFORMING ACTUAL SYNCHRONIZATION - This will add new instruments to the database
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - SEC synchronization completed: SYNC COMPLETED: Added 2 new instruments out of 50 missing symbols. Database now has 102 instruments from SEC data.
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - Starting SEC synchronization - dryRun: true, forceRefresh: true, maxInstruments: 200
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - SEC synchronization (dry-run) completed: DRY RUN: Found 30 missing symbols out of 5000 SEC symbols. Would add 30 new instruments to database.
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - Starting SEC synchronization - dryRun: true, forceRefresh: false, maxInstruments: 100
2025-06-03 01:35:43 [Test worker] ERROR c.i.a.c.InstrumentController - Error during SEC synchronization
java.lang.RuntimeException: Database error
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.api.controller.InstrumentControllerSpec$__spock_feature_0_14_closure4.doCall(InstrumentControllerSpec.groovy:245)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.service.SecSynchronizationService$SpockMock$556441800.synchronizeInstruments(Unknown Source)
	at com.investment.api.controller.InstrumentController.syncSecData(InstrumentController.java:276)
	at com.investment.api.controller.InstrumentController$syncSecData$3.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.InstrumentControllerSpec.$spock_feature_0_14(InstrumentControllerSpec.groovy:248)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - Starting SEC synchronization - dryRun: true, forceRefresh: false, maxInstruments: 1000
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - SEC synchronization (dry-run) completed: DRY RUN: Found 50 missing symbols out of 5000 SEC symbols. Would add 50 new instruments to database.
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - Starting CSV upload processing - file: instruments.csv, size: 289 bytes, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - CSV validation completed: DRY RUN: Processed 2 valid rows out of 2 total rows. Would add/update 2 instruments. Found 0 validation errors.
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - Starting CSV upload processing - file: instruments.csv, size: 191 bytes, dryRun: false, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-06-03 01:35:43 [Test worker] WARN  c.i.a.c.InstrumentController - PERFORMING ACTUAL CSV IMPORT - This will add/update instruments in the database
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - CSV import completed: CSV UPLOAD COMPLETED: Processed 1 instruments from 1 valid rows. Added 1 new instruments, updated 0 existing instruments, skipped 0 duplicates.
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - Starting CSV upload processing - file: invalid.csv, size: 15 bytes, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-06-03 01:35:43 [Test worker] ERROR c.i.a.c.InstrumentController - Error during CSV processing
java.lang.RuntimeException: Processing error
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.api.controller.InstrumentControllerSpec$__spock_feature_0_20_closure5.doCall(InstrumentControllerSpec.groovy:337)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.service.CsvInstrumentService$SpockMock$490744610.processCsvFile(Unknown Source)
	at com.investment.api.controller.InstrumentController.uploadCsv(InstrumentController.java:355)
	at com.investment.api.controller.InstrumentController$uploadCsv$4.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at com.investment.api.controller.InstrumentControllerSpec.$spock_feature_0_20(InstrumentControllerSpec.groovy:340)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - Starting CSV upload processing - file: instruments.csv, size: 22 bytes, dryRun: false, maxInstruments: 500, skipDuplicates: false, validateData: false
2025-06-03 01:35:43 [Test worker] WARN  c.i.a.c.InstrumentController - PERFORMING ACTUAL CSV IMPORT - This will add/update instruments in the database
2025-06-03 01:35:43 [Test worker] INFO  c.i.a.c.InstrumentController - CSV import completed: CSV UPLOAD COMPLETED: Processed 1 instruments from 1 valid rows. Added 0 new instruments, updated 1 existing instruments, skipped 0 duplicates.
2025-06-03 01:35:43 [Test worker] INFO  c.i.api.controller.OHLCVController - Retrieving OHLCV data for symbol: AAPL, from 2025-05-24 to 2025-06-03
2025-06-03 01:35:43 [Test worker] INFO  c.i.api.controller.OHLCVController - Retrieving OHLCV data for symbol: UNKNOWN, from 2025-05-24 to 2025-06-03
2025-06-03 01:35:43 [Test worker] INFO  c.i.api.controller.OHLCVController - Updating OHLCV data for 3 symbols
2025-06-03 01:35:43 [Test worker] INFO  c.i.api.controller.OHLCVController - Updating OHLCV data for symbol: AAPL
2025-06-03 01:35:43 [Test worker] INFO  c.i.api.controller.OHLCVController - Starting OHLCV refresh for all instruments - request: RefreshAllRequest{dryRun=true, maxSymbols=100, skipExisting=false, startIndex=0, endIndex=null}
2025-06-03 01:35:43 [Test worker] INFO  c.i.api.controller.OHLCVController - OHLCV refresh validation completed: DRY RUN: Would process 10 out of 50 instruments (range: 0-99). Skipped 5 symbols with recent data. Estimated 10 successful updates, 0 failures.
2025-06-03 01:35:43 [Test worker] INFO  c.i.api.controller.OHLCVController - Starting OHLCV refresh for all instruments - request: RefreshAllRequest{dryRun=false, maxSymbols=50, skipExisting=true, startIndex=0, endIndex=null}
2025-06-03 01:35:43 [Test worker] WARN  c.i.api.controller.OHLCVController - PERFORMING ACTUAL OHLCV REFRESH - This will download data from external APIs and may take significant time
2025-06-03 01:35:43 [Test worker] INFO  c.i.api.controller.OHLCVController - OHLCV refresh completed: REFRESH COMPLETED: Processed 25 out of 100 instruments (range: 0-49). Successfully updated 20 symbols (150 data points), failed 5 symbols, skipped 10 symbols with recent data.
2025-06-03 01:35:43 [Test worker] INFO  c.i.api.controller.OHLCVController - Starting OHLCV refresh for all instruments - request: RefreshAllRequest{dryRun=true, maxSymbols=100, skipExisting=false, startIndex=0, endIndex=null}
2025-06-03 01:35:43 [Test worker] ERROR c.i.api.controller.OHLCVController - Error during OHLCV refresh operation
java.lang.RuntimeException: Service error
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.api.controller.OHLCVControllerSpec$__spock_feature_0_8_closure1.doCall(OHLCVControllerSpec.groovy:174)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.service.OHLCVService$SpockMock$262346085.refreshAllOHLCVData(Unknown Source)
	at com.investment.api.controller.OHLCVController.refreshAllOHLCVData(OHLCVController.java:242)
	at com.investment.api.controller.OHLCVController$refreshAllOHLCVData$2.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.OHLCVControllerSpec.$spock_feature_0_8(OHLCVControllerSpec.groovy:177)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-03 01:35:43 [Test worker] INFO  c.i.api.controller.OHLCVController - Starting OHLCV refresh for all instruments - request: RefreshAllRequest{dryRun=true, maxSymbols=100, skipExisting=false, startIndex=0, endIndex=null}
2025-06-03 01:35:43 [Test worker] INFO  c.i.api.controller.OHLCVController - OHLCV refresh validation completed: DRY RUN: Would process 5 out of 10 instruments (range: 0-99). Skipped 2 symbols with recent data. Estimated 5 successful updates, 0 failures.
2025-06-03 01:35:43 [Test worker] INFO  c.i.api.controller.OHLCVController - Starting OHLCV refresh for all instruments - request: RefreshAllRequest{dryRun=true, maxSymbols=50, skipExisting=true, startIndex=0, endIndex=null}
2025-06-03 01:35:43 [Test worker] INFO  c.i.api.controller.OHLCVController - OHLCV refresh validation completed: DRY RUN: Would process 5 out of 20 instruments (range: 0-49). Skipped 10 symbols with recent data. Estimated 5 successful updates, 0 failures.
2025-06-03 01:35:43 [Test worker] INFO  c.i.api.controller.OHLCVController - Starting OHLCV refresh for all instruments - request: RefreshAllRequest{dryRun=true, maxSymbols=100, skipExisting=false, startIndex=1000, endIndex=1500}
2025-06-03 01:35:43 [Test worker] INFO  c.i.api.controller.OHLCVController - OHLCV refresh validation completed: DRY RUN: Would process 500 out of 5000 instruments (range: 1000-1499). Skipped 50 symbols with recent data. Estimated 450 successful updates, 0 failures.
2025-06-03 01:35:43 [Test worker] INFO  c.i.database.DatabaseManager - Connected to the database
2025-06-03 01:35:43 [Test worker] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-06-03 01:35:43 [Test worker] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-06-03 01:35:43 [Test worker] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 3
2025-06-03 01:35:43 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-06-03 01:35:43 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-06-03 01:35:43 [Test worker] INFO  c.i.database.DatabaseManager - Schema already up to date
2025-06-03 01:35:43 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 3 - adding Bollinger Band columns to OHLCV table
2025-06-03 01:35:43 [Test worker] INFO  c.i.database.DatabaseManager - Bollinger Band columns already exist in OHLCV table
2025-06-03 01:35:43 [Test worker] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-06-03 01:35:43 [Test worker] INFO  c.i.database.DatabaseManager - Database connection closed
2025-06-03 01:38:06 [Test worker] INFO  c.i.database.DatabaseManager - Connected to the database
2025-06-03 01:38:06 [Test worker] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-06-03 01:38:06 [Test worker] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-06-03 01:38:06 [Test worker] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 3
2025-06-03 01:38:06 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-06-03 01:38:06 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Schema already up to date
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 3 - adding Bollinger Band columns to OHLCV table
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Bollinger Band columns already exist in OHLCV table
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Saved 50 OHLCV data points for AAPL
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=FULL_RECALCULATION, forceRecalculate=false (deprecated)}
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 2 total instruments in database
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 25 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 25 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 50 records updated, 0 failed, 0 insufficient data, 0 skipped in 26ms
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Saved 10 OHLCV data points for AAPL
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 2 total instruments in database
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 5 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 5 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 10 records updated, 0 failed, 0 insufficient data, 0 skipped in 12ms
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=FULL_RECALCULATION, forceRecalculate=false (deprecated)}
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 2 total instruments in database
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 30 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 30 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 60 records updated, 0 failed, 0 insufficient data, 0 skipped in 15ms
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=SKIP_EXISTING, forceRecalculate=false (deprecated)}
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 2 total instruments in database
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 0 symbols processed, 0 records updated, 0 failed, 0 insufficient data, 2 skipped in 2ms
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=FULL_RECALCULATION, forceRecalculate=true (deprecated)}
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 2 total instruments in database
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 30 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 30 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 60 records updated, 0 failed, 0 insufficient data, 0 skipped in 15ms
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=true, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 2 total instruments in database
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 19 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 19 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 38 records updated, 0 failed, 0 insufficient data, 0 skipped in 4ms
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Database connection closed
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 1 symbols with OHLCV data out of 1 total instruments in database
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 10 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 1 symbols processed, 10 records updated, 0 failed, 0 insufficient data, 0 skipped in 4ms
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=FULL_RECALCULATION, forceRecalculate=false (deprecated)}
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 1 symbols with OHLCV data out of 1 total instruments in database
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 25 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 1 symbols processed, 25 records updated, 0 failed, 0 insufficient data, 0 skipped in 0ms
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=SKIP_EXISTING, forceRecalculate=false (deprecated)}
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 2 total instruments in database
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 15 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 1 symbols processed, 15 records updated, 0 failed, 0 insufficient data, 1 skipped in 1ms
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=FULL_RECALCULATION, forceRecalculate=true (deprecated)}
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 1 symbols with OHLCV data out of 1 total instruments in database
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 25 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 1 symbols processed, 25 records updated, 0 failed, 0 insufficient data, 0 skipped in 0ms
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=SKIP_EXISTING, forceRecalculate=false (deprecated)}
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 1 symbols with OHLCV data out of 1 total instruments in database
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 0 symbols processed, 0 records updated, 0 failed, 0 insufficient data, 1 skipped in 0ms
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=true, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 1 symbols with OHLCV data out of 1 total instruments in database
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 5 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 1 symbols processed, 5 records updated, 0 failed, 0 insufficient data, 0 skipped in 1ms
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=SKIP_EXISTING, forceRecalculate=false (deprecated)}
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 3 symbols with OHLCV data out of 5 total instruments in database
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Optimization: Processing only symbols with OHLCV data, skipping 2 instruments without price data
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 100 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 150 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for GOOGL: 200 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 3 symbols processed, 450 records updated, 0 failed, 0 insufficient data, 0 skipped in 0ms
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=50, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 3 symbols with OHLCV data out of 3 total instruments in database
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 100 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 75 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 175 records updated, 0 failed, 1 insufficient data, 0 skipped in 0ms
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=SKIP_EXISTING, forceRecalculate=false (deprecated)}
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 2 total instruments in database
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 150 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 1 symbols processed, 150 records updated, 0 failed, 0 insufficient data, 1 skipped in 0ms
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 3 symbols with OHLCV data out of 3 total instruments in database
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 100 records updated
2025-06-03 01:38:07 [Test worker] ERROR c.i.service.BollingerBandService - Error processing symbol FAIL
java.sql.SQLException: Database error
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.service.BollingerBandServiceSpec$__spock_feature_0_3_closure1.doCall(BollingerBandServiceSpec.groovy:118)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.database.DatabaseManager$SpockMock$1817194074.calculateAndUpdateBollingerBandsIncremental(Unknown Source)
	at com.investment.service.BollingerBandService.calculateBollingerBandsForSymbol(BollingerBandService.java:198)
	at com.investment.service.BollingerBandService.calculateBollingerBands(BollingerBandService.java:93)
	at com.investment.service.BollingerBandService$calculateBollingerBands.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.service.BollingerBandServiceSpec.$spock_feature_0_3(BollingerBandServiceSpec.groovy:122)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 150 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 250 records updated, 1 failed, 0 insufficient data, 0 skipped in 5ms
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=2, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 4 symbols with OHLCV data out of 4 total instruments in database
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Limited processing to 2 symbols
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 100 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 150 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 250 records updated, 0 failed, 0 insufficient data, 0 skipped in 1ms
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=true, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 2 total instruments in database
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 100 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 150 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 250 records updated, 0 failed, 0 insufficient data, 0 skipped in 0ms
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:38:07 [Test worker] ERROR c.i.service.BollingerBandService - Fatal error during Bollinger Band calculation
java.lang.RuntimeException: Database connection failed
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.service.BollingerBandServiceSpec$__spock_feature_0_6_closure2.doCall(BollingerBandServiceSpec.groovy:185)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.database.DatabaseManager$SpockMock$1817194074.getSymbolsWithOhlcvData(Unknown Source)
	at com.investment.service.BollingerBandService.calculateBollingerBands(BollingerBandService.java:50)
	at com.investment.service.BollingerBandService$calculateBollingerBands.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.service.BollingerBandServiceSpec.$spock_feature_0_6(BollingerBandServiceSpec.groovy:188)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Starting Bollinger Band calculation with parameters: BollingerBandRequest{period=20, stdDevMultiplier=2.0, dryRun=false, maxSymbols=0, minDataPoints=20, calculationMode=INCREMENTAL, forceRecalculate=false (deprecated)}
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Found 2 symbols with OHLCV data out of 5 total instruments in database
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Optimization: Processing only symbols with OHLCV data, skipping 3 instruments without price data
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for AAPL: 100 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Successfully calculated Bollinger Bands for MSFT: 150 records updated
2025-06-03 01:38:07 [Test worker] INFO  c.i.service.BollingerBandService - Bollinger Band calculation completed: 2 symbols processed, 250 records updated, 0 failed, 0 insufficient data, 0 skipped in 0ms
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Connected to the database
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 3
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Schema already up to date
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 3 - adding Bollinger Band columns to OHLCV table
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Bollinger Band columns already exist in OHLCV table
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Saved 25 OHLCV data points for AAPL
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Saved 22 OHLCV data points for MSFT
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Saved 5 OHLCV data points for NEWCO
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Database connection closed
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Connected to the database
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 3
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Schema already up to date
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 3 - adding Bollinger Band columns to OHLCV table
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Bollinger Band columns already exist in OHLCV table
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Saved 25 OHLCV data points for AAPL
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Saved 22 OHLCV data points for MSFT
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Saved 5 OHLCV data points for NEWCO
2025-06-03 01:38:07 [Test worker] INFO  c.i.database.DatabaseManager - Database connection closed
