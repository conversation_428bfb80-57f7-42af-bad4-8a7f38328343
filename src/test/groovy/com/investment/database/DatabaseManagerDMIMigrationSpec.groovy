package com.investment.database

import com.investment.model.OHLCV
import spock.lang.Specification
import spock.lang.Shared

import java.time.LocalDate

/**
 * Test for DMI columns migration in DatabaseManager.
 */
class DatabaseManagerDMIMigrationSpec extends Specification {

    @Shared
    DatabaseManager databaseManager

    def setupSpec() {
        // Clean up any existing test database
        new File("./data/dmi_migration_test.duckdb").delete()
        
        // Initialize test database
        DatabaseManager.setDbUrl("********************************************")
        databaseManager = new DatabaseManager()
        databaseManager.initDatabase()
    }

    def cleanupSpec() {
        databaseManager?.closeConnection()
        // Clean up test database file
        new File("./data/dmi_migration_test.duckdb").delete()
    }

    def "should successfully migrate to version 4 with DMI columns"() {
        given: "a fresh database"
        // Database is already initialized in setupSpec

        when: "checking the schema version"
        int version = databaseManager.getCurrentSchemaVersion()

        then: "should be at version 4"
        version == 4
    }

    def "should create OHLCV table with DMI columns"() {
        given: "an initialized database"
        // Database is already initialized

        when: "inserting test data with DMI values"
        def testData = [
            new OHLCV("TEST", LocalDate.now(), 100.0, 105.0, 98.0, 102.0, 1000000L,
                     null, null, null, null, // BB values
                     25.5, 15.3, 45.2), // DMI values
            new OHLCV("TEST", LocalDate.now().minusDays(1), 98.0, 103.0, 96.0, 100.0, 900000L,
                     null, null, null, null, // BB values  
                     null, null, null) // DMI values (null)
        ]
        
        and: "saving the instrument first"
        databaseManager.saveInstrument("TEST", "Test Company", "Technology")
        
        and: "saving the OHLCV data"
        databaseManager.saveOHLCVData(testData)

        then: "data should be saved successfully"
        noExceptionThrown()

        when: "retrieving the data"
        List<OHLCV> retrievedData = databaseManager.getOHLCVData("TEST", 
                                                                 LocalDate.now().minusDays(2), 
                                                                 LocalDate.now().plusDays(1))

        then: "should retrieve data with correct DMI values"
        retrievedData.size() == 2
        
        and: "first record should have DMI values"
        def record1 = retrievedData.find { it.date == LocalDate.now() }
        record1.dmiPlusDi == 25.5
        record1.dmiMinusDi == 15.3
        record1.dmiAdx == 45.2
        
        and: "second record should have null DMI values"
        def record2 = retrievedData.find { it.date == LocalDate.now().minusDays(1) }
        record2.dmiPlusDi == null
        record2.dmiMinusDi == null
        record2.dmiAdx == null
    }

    def "should handle mixed Bollinger Band and DMI data"() {
        given: "test data with both BB and DMI values"
        def testData = [
            new OHLCV("MIXED", LocalDate.now(), 150.0, 155.0, 148.0, 152.0, 2000000L,
                     150.5, 2.1, 154.7, 146.3, // BB values
                     28.7, 18.9, 42.1) // DMI values
        ]
        
        when: "saving the instrument and data"
        databaseManager.saveInstrument("MIXED", "Mixed Indicators Company", "Technology")
        databaseManager.saveOHLCVData(testData)

        then: "data should be saved successfully"
        noExceptionThrown()

        when: "retrieving the data"
        List<OHLCV> retrievedData = databaseManager.getOHLCVData("MIXED", 
                                                                 LocalDate.now().minusDays(1), 
                                                                 LocalDate.now().plusDays(1))

        then: "should retrieve data with both BB and DMI values"
        retrievedData.size() == 1
        def record = retrievedData[0]
        
        and: "Bollinger Band values should be correct"
        record.bbMiddleBand == 150.5
        record.bbStdDev == 2.1
        record.bbUpperBand == 154.7
        record.bbLowerBand == 146.3
        
        and: "DMI values should be correct"
        record.dmiPlusDi == 28.7
        record.dmiMinusDi == 18.9
        record.dmiAdx == 42.1
    }

    def "should handle OHLCV data with only basic price data"() {
        given: "test data with only basic OHLCV values"
        def testData = [
            new OHLCV("BASIC", LocalDate.now(), 200.0, 205.0, 198.0, 202.0, 1500000L)
        ]
        
        when: "saving the instrument and data"
        databaseManager.saveInstrument("BASIC", "Basic Data Company", "Technology")
        databaseManager.saveOHLCVData(testData)

        then: "data should be saved successfully"
        noExceptionThrown()

        when: "retrieving the data"
        List<OHLCV> retrievedData = databaseManager.getOHLCVData("BASIC", 
                                                                 LocalDate.now().minusDays(1), 
                                                                 LocalDate.now().plusDays(1))

        then: "should retrieve data with null technical indicator values"
        retrievedData.size() == 1
        def record = retrievedData[0]
        
        and: "basic OHLCV values should be correct"
        record.symbol == "BASIC"
        record.open == 200.0
        record.high == 205.0
        record.low == 198.0
        record.close == 202.0
        record.volume == 1500000L
        
        and: "all technical indicator values should be null"
        record.bbMiddleBand == null
        record.bbStdDev == null
        record.bbUpperBand == null
        record.bbLowerBand == null
        record.dmiPlusDi == null
        record.dmiMinusDi == null
        record.dmiAdx == null
    }

    def "should update existing records with DMI data"() {
        given: "existing record without DMI data"
        def initialData = [
            new OHLCV("UPDATE", LocalDate.now(), 300.0, 305.0, 298.0, 302.0, 800000L)
        ]
        
        when: "saving initial data"
        databaseManager.saveInstrument("UPDATE", "Update Test Company", "Technology")
        databaseManager.saveOHLCVData(initialData)

        then: "initial data should be saved"
        def initialRetrieved = databaseManager.getOHLCVData("UPDATE", 
                                                           LocalDate.now().minusDays(1), 
                                                           LocalDate.now().plusDays(1))
        initialRetrieved.size() == 1
        initialRetrieved[0].dmiPlusDi == null

        when: "updating with DMI data"
        def updatedData = [
            new OHLCV("UPDATE", LocalDate.now(), 300.0, 305.0, 298.0, 302.0, 800000L,
                     null, null, null, null, // BB values
                     32.1, 22.8, 38.5) // DMI values
        ]
        databaseManager.saveOHLCVData(updatedData)

        then: "data should be updated with DMI values"
        def updatedRetrieved = databaseManager.getOHLCVData("UPDATE", 
                                                           LocalDate.now().minusDays(1), 
                                                           LocalDate.now().plusDays(1))
        updatedRetrieved.size() == 1
        def record = updatedRetrieved[0]
        record.dmiPlusDi == 32.1
        record.dmiMinusDi == 22.8
        record.dmiAdx == 38.5
    }
}
