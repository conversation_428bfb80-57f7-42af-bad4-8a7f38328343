package com.investment.service

import com.investment.api.model.DMIRequest
import com.investment.api.model.DMIResponse
import com.investment.database.DatabaseManager
import spock.lang.Specification
import spock.lang.Subject

/**
 * Unit tests for DMIService.
 */
class DMIServiceSpec extends Specification {

    DatabaseManager mockDatabaseManager = Mock()
    
    @Subject
    DMIService dmiService = new DMIService(mockDatabaseManager)

    def "should use INCREMENTAL mode by default"() {
        given: "a default DMI request"
        def request = new DMIRequest()
        
        and: "mock database returns symbols"
        mockDatabaseManager.getSymbolsWithOhlcvData() >> ["AAPL", "MSFT"]
        mockDatabaseManager.countOhlcvRecords(_) >> 50
        mockDatabaseManager.hasExistingDMIData(_) >> false
        mockDatabaseManager.calculateAndUpdateDMIIncremental(_, _, _) >> 10

        when: "calculating DMI"
        DMIResponse response = dmiService.calculateDMI(request)

        then: "should use INCREMENTAL mode"
        response.parameters.calculationMode == DMIRequest.CalculationMode.INCREMENTAL
        response.status == "success"
        response.processedSymbols == 2
    }

    def "should use FULL_RECALCULATION mode when forceRecalculate is true"() {
        given: "a request with forceRecalculate=true"
        def request = new DMIRequest()
        request.forceRecalculate = true
        
        and: "mock database returns symbols"
        mockDatabaseManager.getSymbolsWithOhlcvData() >> ["AAPL"]
        mockDatabaseManager.countOhlcvRecords(_) >> 50
        mockDatabaseManager.clearDMIData(_) >> 5
        mockDatabaseManager.calculateAndUpdateDMI(_, _, _) >> 15

        when: "calculating DMI"
        DMIResponse response = dmiService.calculateDMI(request)

        then: "should use FULL_RECALCULATION mode"
        response.parameters.calculationMode == DMIRequest.CalculationMode.FULL_RECALCULATION
        response.status == "success"
        
        and: "should clear existing data"
        1 * mockDatabaseManager.clearDMIData("AAPL")
    }

    def "should skip symbols with insufficient data"() {
        given: "a request with minimum data points requirement"
        def request = new DMIRequest(14, false)
        request.minDataPoints = 30
        
        and: "mock database returns symbols with varying data counts"
        mockDatabaseManager.getSymbolsWithOhlcvData() >> ["AAPL", "MSFT", "GOOGL"]
        mockDatabaseManager.countOhlcvRecords("AAPL") >> 50  // sufficient
        mockDatabaseManager.countOhlcvRecords("MSFT") >> 20  // insufficient
        mockDatabaseManager.countOhlcvRecords("GOOGL") >> 35 // sufficient
        mockDatabaseManager.hasExistingDMIData(_) >> false
        mockDatabaseManager.calculateAndUpdateDMIIncremental(_, _, _) >> 10

        when: "calculating DMI"
        DMIResponse response = dmiService.calculateDMI(request)

        then: "should skip symbols with insufficient data"
        response.symbolsWithInsufficientData == ["MSFT"]
        response.processedSymbols == 2
        response.status == "success"
    }

    def "should skip symbols with existing data in SKIP_EXISTING mode"() {
        given: "a request with SKIP_EXISTING mode"
        def request = new DMIRequest()
        request.calculationMode = DMIRequest.CalculationMode.SKIP_EXISTING
        
        and: "mock database returns symbols with some having existing data"
        mockDatabaseManager.getSymbolsWithOhlcvData() >> ["AAPL", "MSFT", "GOOGL"]
        mockDatabaseManager.countOhlcvRecords(_) >> 50
        mockDatabaseManager.hasExistingDMIData("AAPL") >> true   // has data
        mockDatabaseManager.hasExistingDMIData("MSFT") >> false  // no data
        mockDatabaseManager.hasExistingDMIData("GOOGL") >> true  // has data
        mockDatabaseManager.calculateAndUpdateDMI(_, _, _) >> 10

        when: "calculating DMI"
        DMIResponse response = dmiService.calculateDMI(request)

        then: "should skip symbols with existing data"
        response.skippedSymbols == ["AAPL", "GOOGL"]
        response.processedSymbols == 1
        response.status == "success"
    }

    def "should handle dry run mode correctly"() {
        given: "a dry run request"
        def request = new DMIRequest(14, true)
        
        and: "mock database returns symbols"
        mockDatabaseManager.getSymbolsWithOhlcvData() >> ["AAPL", "MSFT"]
        mockDatabaseManager.countOhlcvRecords(_) >> 50
        mockDatabaseManager.hasExistingDMIData(_) >> false
        mockDatabaseManager.calculateAndUpdateDMIIncremental(_, _, true) >> 25

        when: "calculating DMI"
        DMIResponse response = dmiService.calculateDMI(request)

        then: "should perform dry run"
        response.dryRun == true
        response.totalRecordsUpdated == 50  // 25 * 2 symbols
        response.status == "success"
        
        and: "should not clear any data"
        0 * mockDatabaseManager.clearDMIData(_)
    }

    def "should limit symbols when maxSymbols is specified"() {
        given: "a request with maxSymbols limit"
        def request = new DMIRequest()
        request.maxSymbols = 2
        
        and: "mock database returns more symbols than limit"
        mockDatabaseManager.getSymbolsWithOhlcvData() >> ["AAPL", "MSFT", "GOOGL", "AMZN"]
        mockDatabaseManager.countOhlcvRecords(_) >> 50
        mockDatabaseManager.hasExistingDMIData(_) >> false
        mockDatabaseManager.calculateAndUpdateDMIIncremental(_, _, _) >> 10

        when: "calculating DMI"
        DMIResponse response = dmiService.calculateDMI(request)

        then: "should process only maxSymbols"
        response.processedSymbols == 2
        response.status == "success"
    }

    def "should handle calculation errors gracefully"() {
        given: "a request that will cause errors"
        def request = new DMIRequest()
        
        and: "mock database returns symbols but calculation fails for one"
        mockDatabaseManager.getSymbolsWithOhlcvData() >> ["AAPL", "MSFT"]
        mockDatabaseManager.countOhlcvRecords(_) >> 50
        mockDatabaseManager.hasExistingDMIData(_) >> false
        mockDatabaseManager.calculateAndUpdateDMIIncremental("AAPL", _, _) >> 10
        mockDatabaseManager.calculateAndUpdateDMIIncremental("MSFT", _, _) >> { throw new RuntimeException("Database error") }

        when: "calculating DMI"
        DMIResponse response = dmiService.calculateDMI(request)

        then: "should handle errors gracefully"
        response.processedSymbols == 1
        response.failedSymbols == ["MSFT"]
        response.errors.size() == 1
        response.errors[0].contains("MSFT")
        response.status == "partial_success"
    }

    def "should use correct period and minDataPoints from request"() {
        given: "a request with custom parameters"
        def request = new DMIRequest(21, false)
        request.minDataPoints = 50
        
        and: "mock database returns symbols"
        mockDatabaseManager.getSymbolsWithOhlcvData() >> ["AAPL"]
        mockDatabaseManager.countOhlcvRecords(_) >> 60
        mockDatabaseManager.hasExistingDMIData(_) >> false
        mockDatabaseManager.calculateAndUpdateDMIIncremental(_, _, _) >> 10

        when: "calculating DMI"
        DMIResponse response = dmiService.calculateDMI(request)

        then: "should use correct parameters"
        response.parameters.period == 21
        response.parameters.minDataPoints == 50
        
        and: "should call database with correct period"
        1 * mockDatabaseManager.calculateAndUpdateDMIIncremental("AAPL", 21, false)
    }

    def "should handle different calculation modes correctly"() {
        given: "requests with different calculation modes"
        def incrementalRequest = new DMIRequest()
        incrementalRequest.calculationMode = DMIRequest.CalculationMode.INCREMENTAL
        
        def fullRequest = new DMIRequest()
        fullRequest.calculationMode = DMIRequest.CalculationMode.FULL_RECALCULATION
        
        and: "mock database setup"
        mockDatabaseManager.getSymbolsWithOhlcvData() >> ["AAPL"]
        mockDatabaseManager.countOhlcvRecords(_) >> 50
        mockDatabaseManager.hasExistingDMIData(_) >> false
        mockDatabaseManager.calculateAndUpdateDMIIncremental(_, _, _) >> 10
        mockDatabaseManager.clearDMIData(_) >> 5
        mockDatabaseManager.calculateAndUpdateDMI(_, _, _) >> 15

        when: "calculating with incremental mode"
        DMIResponse incrementalResponse = dmiService.calculateDMI(incrementalRequest)

        then: "should use incremental calculation"
        incrementalResponse.parameters.calculationMode == DMIRequest.CalculationMode.INCREMENTAL
        1 * mockDatabaseManager.calculateAndUpdateDMIIncremental("AAPL", 14, false)

        when: "calculating with full recalculation mode"
        DMIResponse fullResponse = dmiService.calculateDMI(fullRequest)

        then: "should use full calculation and clear data"
        fullResponse.parameters.calculationMode == DMIRequest.CalculationMode.FULL_RECALCULATION
        1 * mockDatabaseManager.clearDMIData("AAPL")
        1 * mockDatabaseManager.calculateAndUpdateDMI("AAPL", 14, false)
    }

    def "should generate correct response summary"() {
        given: "a successful calculation"
        def request = new DMIRequest()
        
        and: "mock database returns symbols"
        mockDatabaseManager.getSymbolsWithOhlcvData() >> ["AAPL", "MSFT"]
        mockDatabaseManager.countOhlcvRecords(_) >> 50
        mockDatabaseManager.hasExistingDMIData(_) >> false
        mockDatabaseManager.calculateAndUpdateDMIIncremental(_, _, _) >> 25

        when: "calculating DMI"
        DMIResponse response = dmiService.calculateDMI(request)

        then: "should generate correct summary"
        response.summary.contains("2 symbols processed")
        response.summary.contains("50 records updated")
        response.summary.contains("DMI calculation completed")
        !response.summary.contains("Dry run")
    }

    def "should generate correct dry run summary"() {
        given: "a dry run request"
        def request = new DMIRequest(14, true)
        
        and: "mock database returns symbols"
        mockDatabaseManager.getSymbolsWithOhlcvData() >> ["AAPL"]
        mockDatabaseManager.countOhlcvRecords(_) >> 50
        mockDatabaseManager.hasExistingDMIData(_) >> false
        mockDatabaseManager.calculateAndUpdateDMIIncremental(_, _, true) >> 25

        when: "calculating DMI"
        DMIResponse response = dmiService.calculateDMI(request)

        then: "should generate correct dry run summary"
        response.summary.contains("Dry run completed")
        response.summary.contains("1 symbols would be processed")
        response.summary.contains("25 records would be updated")
    }
}
