package com.investment.service;

import com.investment.api.model.BollingerBandRequest;
import com.investment.api.model.BollingerBandResponse;
import com.investment.database.DatabaseManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * Service for calculating and updating Bollinger Band technical indicators.
 */
@Service
public class BollingerBandService {
    
    private static final Logger logger = LoggerFactory.getLogger(BollingerBandService.class);
    
    private final DatabaseManager databaseManager;
    
    public BollingerBandService(DatabaseManager databaseManager) {
        this.databaseManager = databaseManager;
    }
    
    /**
     * Calculate and update Bollinger Band indicators for all symbols in the database.
     *
     * @param request The calculation request parameters
     * @return Response containing calculation results and statistics
     */
    public BollingerBandResponse calculateBollingerBands(BollingerBandRequest request) {
        long startTime = System.currentTimeMillis();
        
        logger.info("Starting Bollinger Band calculation with parameters: {}", request);
        
        List<String> symbolsWithInsufficientData = new ArrayList<>();
        List<String> skippedSymbols = new ArrayList<>();
        List<String> failedSymbols = new ArrayList<>();
        List<String> errors = new ArrayList<>();
        
        int processedSymbols = 0;
        int totalRecordsUpdated = 0;
        String status = "success";
        
        try {
            // Get symbols that actually have OHLCV data (optimization)
            List<String> symbolsWithOhlcvData = databaseManager.getSymbolsWithOhlcvData();

            // Also get total instrument count for comparison logging
            List<String> allInstrumentSymbols = databaseManager.getAllSymbols();

            logger.info("Found {} symbols with OHLCV data out of {} total instruments in database",
                       symbolsWithOhlcvData.size(), allInstrumentSymbols.size());

            if (symbolsWithOhlcvData.size() < allInstrumentSymbols.size()) {
                logger.info("Optimization: Processing only symbols with OHLCV data, skipping {} instruments without price data",
                           allInstrumentSymbols.size() - symbolsWithOhlcvData.size());
            }

            // Apply max symbols limit if specified
            List<String> symbolsToProcess = symbolsWithOhlcvData;
            if (request.getMaxSymbols() > 0 && symbolsWithOhlcvData.size() > request.getMaxSymbols()) {
                symbolsToProcess = symbolsWithOhlcvData.subList(0, request.getMaxSymbols());
                logger.info("Limited processing to {} symbols", request.getMaxSymbols());
            }
            
            for (String symbol : symbolsToProcess) {
                try {
                    logger.debug("Processing symbol: {}", symbol);
                    
                    // Check if symbol has sufficient data
                    int recordCount = databaseManager.countOhlcvRecords(symbol);
                    if (recordCount < request.getMinDataPoints()) {
                        symbolsWithInsufficientData.add(symbol);
                        logger.debug("Symbol {} has insufficient data: {} records (minimum: {})", 
                                   symbol, recordCount, request.getMinDataPoints());
                        continue;
                    }
                    
                    // Check if symbol already has Bollinger Band data and forceRecalculate is false
                    if (!request.isForceRecalculate() && hasExistingBollingerBandData(symbol)) {
                        skippedSymbols.add(symbol);
                        logger.debug("Symbol {} already has Bollinger Band data, skipping", symbol);
                        continue;
                    }
                    
                    // Calculate and update Bollinger Bands for this symbol
                    int updatedRecords = calculateBollingerBandsForSymbol(symbol, request);
                    
                    if (updatedRecords > 0) {
                        processedSymbols++;
                        totalRecordsUpdated += updatedRecords;
                        logger.info("Successfully calculated Bollinger Bands for {}: {} records updated", 
                                  symbol, updatedRecords);
                    } else {
                        logger.warn("No records updated for symbol: {}", symbol);
                    }
                    
                } catch (Exception e) {
                    failedSymbols.add(symbol);
                    String errorMsg = String.format("Failed to process symbol %s: %s", symbol, e.getMessage());
                    errors.add(errorMsg);
                    logger.error("Error processing symbol {}", symbol, e);
                }
            }
            
            long processingTime = System.currentTimeMillis() - startTime;
            
            // Determine overall status
            if (!failedSymbols.isEmpty()) {
                status = failedSymbols.size() == symbolsToProcess.size() ? "failed" : "partial_success";
            }
            
            logger.info("Bollinger Band calculation completed: {} symbols processed, {} records updated, {} failed, {} insufficient data, {} skipped in {}ms",
                       processedSymbols, totalRecordsUpdated, failedSymbols.size(), 
                       symbolsWithInsufficientData.size(), skippedSymbols.size(), processingTime);
            
            return new BollingerBandResponse(
                    status,
                    processedSymbols,
                    totalRecordsUpdated,
                    symbolsWithInsufficientData,
                    skippedSymbols,
                    failedSymbols,
                    processingTime,
                    errors,
                    request.isDryRun(),
                    new BollingerBandResponse.CalculationParameters(
                            request.getPeriod(),
                            request.getStdDevMultiplier(),
                            request.getMinDataPoints()
                    )
            );
            
        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            logger.error("Fatal error during Bollinger Band calculation", e);
            
            errors.add("Fatal error: " + e.getMessage());
            
            return new BollingerBandResponse(
                    "failed",
                    processedSymbols,
                    totalRecordsUpdated,
                    symbolsWithInsufficientData,
                    skippedSymbols,
                    failedSymbols,
                    processingTime,
                    errors,
                    request.isDryRun(),
                    new BollingerBandResponse.CalculationParameters(
                            request.getPeriod(),
                            request.getStdDevMultiplier(),
                            request.getMinDataPoints()
                    )
            );
        }
    }
    
    /**
     * Check if a symbol already has Bollinger Band data.
     */
    private boolean hasExistingBollingerBandData(String symbol) throws SQLException {
        return databaseManager.hasExistingBollingerBandData(symbol);
    }
    
    /**
     * Calculate Bollinger Bands for a specific symbol.
     */
    private int calculateBollingerBandsForSymbol(String symbol, BollingerBandRequest request) throws SQLException {
        return databaseManager.calculateAndUpdateBollingerBands(
                symbol, 
                request.getPeriod(), 
                request.getStdDevMultiplier(),
                request.isDryRun()
        );
    }
}
