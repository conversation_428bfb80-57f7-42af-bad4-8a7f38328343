package com.investment.model;

import java.time.LocalDate;

public class OHLCV {
    private final String symbol;
    private final LocalDate date;
    private final double open;
    private final double high;
    private final double low;
    private final double close;
    private final long volume;
    private final Double bbMiddleBand;
    private final Double bbStdDev;
    private final Double bbUpperBand;
    private final Double bbLowerBand;
    
    // Constructor for backward compatibility (without Bollinger Bands)
    public OHLCV(String symbol, LocalDate date, double open, double high, double low, double close, long volume) {
        this(symbol, date, open, high, low, close, volume, null, null, null, null);
    }

    // Full constructor with Bollinger Band data
    public OHLCV(String symbol, LocalDate date, double open, double high, double low, double close, long volume,
                 Double bbMiddleBand, Double bbStdDev, Double bbUpperBand, Double bbLowerBand) {
        this.symbol = symbol;
        this.date = date;
        this.open = open;
        this.high = high;
        this.low = low;
        this.close = close;
        this.volume = volume;
        this.bbMiddleBand = bbMiddleBand;
        this.bbStdDev = bbStdDev;
        this.bbUpperBand = bbUpperBand;
        this.bbLowerBand = bbLowerBand;
    }
    
    public String getSymbol() {
        return symbol;
    }
    
    public LocalDate getDate() {
        return date;
    }
    
    public double getOpen() {
        return open;
    }
    
    public double getHigh() {
        return high;
    }
    
    public double getLow() {
        return low;
    }
    
    public double getClose() {
        return close;
    }
    
    public long getVolume() {
        return volume;
    }

    public Double getBbMiddleBand() {
        return bbMiddleBand;
    }

    public Double getBbStdDev() {
        return bbStdDev;
    }

    public Double getBbUpperBand() {
        return bbUpperBand;
    }

    public Double getBbLowerBand() {
        return bbLowerBand;
    }
    
    @Override
    public String toString() {
        return "OHLCV{" +
                "symbol='" + symbol + '\'' +
                ", date=" + date +
                ", open=" + open +
                ", high=" + high +
                ", low=" + low +
                ", close=" + close +
                ", volume=" + volume +
                ", bbMiddleBand=" + bbMiddleBand +
                ", bbStdDev=" + bbStdDev +
                ", bbUpperBand=" + bbUpperBand +
                ", bbLowerBand=" + bbLowerBand +
                '}';
    }
}
